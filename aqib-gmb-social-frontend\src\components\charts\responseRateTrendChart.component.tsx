import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import { Line } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface IResponseRateTrendChartProps {
  data: Record<
    string,
    { total: number; replied: number; responseRate: number }
  >;
  title?: string;
}

const ResponseRateTrendChart: React.FC<IResponseRateTrendChartProps> = ({
  data,
  title = "Weekly Response Rate Trend",
}) => {
  const theme = useTheme();

  // Prepare chart data
  const weeks = Object.keys(data).sort();
  const responseRateData = weeks.map((week) => data[week]?.responseRate || 0);

  const chartData = {
    labels: weeks.map((week) => {
      const date = new Date(week);
      return `Week of ${date.toLocaleDateString()}`;
    }),
    datasets: [
      {
        label: "Response Rate (%)",
        data: responseRateData,
        borderColor: theme.palette.success.main,
        backgroundColor: theme.palette.success.main + "20",
        fill: true,
        tension: 0.4,
        pointRadius: 5,
        pointHoverRadius: 8,
        pointBackgroundColor: theme.palette.success.main,
        pointBorderColor: "#fff",
        pointBorderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          usePointStyle: true,
          pointStyle: "rect",
          boxWidth: 12,
          boxHeight: 12,
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          title: function (context: any) {
            return context[0].label;
          },
          label: function (context: any) {
            const weekKey = weeks[context.dataIndex];
            const weekData = data[weekKey];
            return [
              `Response Rate: ${context.parsed.y.toFixed(1)}%`,
              `Reviews: ${weekData?.total || 0}`,
              `Replies: ${weekData?.replied || 0}`,
            ];
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Week",
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Response Rate (%)",
        },
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function (value: any) {
            return value + "%";
          },
        },
      },
    },
  };

  // Calculate summary statistics
  const avgResponseRate =
    weeks.length > 0
      ? responseRateData.reduce((sum, rate) => sum + rate, 0) / weeks.length
      : 0;

  const maxResponseRate = Math.max(...responseRateData, 0);
  const minResponseRate =
    responseRateData.length > 0 ? Math.min(...responseRateData) : 0;

  const totalReviews = weeks.reduce(
    (sum, week) => sum + (data[week]?.total || 0),
    0
  );
  const totalReplies = weeks.reduce(
    (sum, week) => sum + (data[week]?.replied || 0),
    0
  );

  // Determine trend
  const firstHalf = responseRateData.slice(
    0,
    Math.ceil(responseRateData.length / 2)
  );
  const secondHalf = responseRateData.slice(
    Math.ceil(responseRateData.length / 2)
  );
  const firstHalfAvg =
    firstHalf.length > 0
      ? firstHalf.reduce((sum, rate) => sum + rate, 0) / firstHalf.length
      : 0;
  const secondHalfAvg =
    secondHalf.length > 0
      ? secondHalf.reduce((sum, rate) => sum + rate, 0) / secondHalf.length
      : 0;
  const trendDirection =
    secondHalfAvg > firstHalfAvg
      ? "improving"
      : secondHalfAvg < firstHalfAvg
      ? "declining"
      : "stable";

  const getTrendColor = () => {
    switch (trendDirection) {
      case "improving":
        return theme.palette.success.main;
      case "declining":
        return theme.palette.error.main;
      default:
        return theme.palette.grey[600];
    }
  };

  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 4, mb: 2, flexWrap: "wrap" }}>
          <Typography variant="body2" color="text.secondary">
            Average: <strong>{avgResponseRate.toFixed(1)}%</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Best Week: <strong>{maxResponseRate.toFixed(1)}%</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Lowest Week: <strong>{minResponseRate.toFixed(1)}%</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total Reviews: <strong>{totalReviews}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total Replies: <strong>{totalReplies}</strong>
          </Typography>
        </Box>
        <Typography
          variant="body2"
          sx={{
            color: getTrendColor(),
            fontWeight: "medium",
            textTransform: "capitalize",
          }}
        >
          Trend: {trendDirection}
        </Typography>
      </Box>

      {weeks.length > 0 ? (
        <Line data={chartData} options={options} />
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No data available for the selected period
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ResponseRateTrendChart;
