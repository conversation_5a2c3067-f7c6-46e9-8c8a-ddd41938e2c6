# Google Post Video Upload Test Guide

## Overview
This guide helps test the newly implemented video upload functionality for Google posts.

## Changes Made

### Frontend Changes
1. **Google Post Form Component** (`googlePostForm.component.tsx`)
   - Updated icon from `ImageOutlined` to `PermMediaOutlined` to represent both images and videos
   - Updated text from "Add/Edit Post Image" to "Add/Edit Post Media"
   - Updated description to show video format support: "Images: JPG, PNG (Max 35MB) | Videos: MP4, MOV (Max 100MB, 30 seconds)"

2. **Create Social Post Screen** (`createSocialPost.screen.tsx`)
   - Updated validation schema to support video formats (MP4, MOV)
   - Updated file size limits: 35MB for images, 100MB for videos
   - Updated allowed file types for Google posts (tabValue === 1) to include videos
   - Updated error messages to reflect video support

3. **Application Constants** (`application.constant.tsx`)
   - Added `GOOGLE_POST_MEDIA_FORMATS` with supported image and video formats
   - Added `GOOGLE_POST_LIMITS` with size and duration limits

### Backend Changes
1. **Posts Controller** (`posts.controller.js`)
   - Updated allowed file types to include video/mp4 and video/mov
   - Updated file size validation: 35MB for images, 100MB for videos

## Test Cases

### 1. Video Upload Test
- Navigate to Create Posts → Google tab
- Click on the media upload area
- Select "Select from Computer"
- Choose a video file (MP4 or MOV format)
- Verify the file is accepted and shows in the form

### 2. File Size Validation Test
- Try uploading a video larger than 100MB
- Verify error message appears: "File size too large... Maximum allowed: 100MB"
- Try uploading an image larger than 35MB
- Verify error message appears: "File size too large... Maximum allowed: 35MB"

### 3. File Format Validation Test
- Try uploading unsupported video formats (AVI, WMV, etc.)
- Verify error message: "Only JPG, PNG images and MP4, MOV videos are allowed."

### 4. Gallery Selection Test
- Click on media upload area
- Select "Select Media from Gallery"
- Verify videos are displayed with video icon
- Select a video from gallery
- Verify it's properly selected and used

### 5. Mixed Media Test
- Upload both images and videos in the same post
- Verify both are accepted and displayed properly

## Expected Behavior

### UI Changes
- Media upload area shows "Add/Edit Post Media" instead of "Add/Edit Post Image"
- Description shows support for both images and videos with their respective limits
- Icon changed to represent general media (not just images)

### Validation
- Images: JPG, PNG formats, max 35MB
- Videos: MP4, MOV formats, max 100MB
- Error messages are specific to file type and size limits

### Gallery
- Videos display with video icon placeholder
- Both images and videos can be selected from gallery

## Google Business Profile Requirements
Based on research, Google Business Profile supports:
- **Video formats**: MP4, MOV
- **Video size**: Up to 100MB
- **Video duration**: Up to 30 seconds (recommended)
- **Image formats**: JPG, PNG
- **Image size**: Up to 35MB

## Notes
- The gallery component already had video support implemented
- Backend manage assets already supported video uploads
- Main changes were in the Google post form validation and UI text
- Constants were added for maintainability and consistency
