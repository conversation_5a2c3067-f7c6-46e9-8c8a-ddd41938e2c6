const pool = require("../config/db");
const ReportScheduler = require("../models/reportScheduler.models");
const emailService = require("./emailService");
const emailTemplateService = require("./emailTemplateService");
const reportGenerationService = require("./reportGenerationService");
const transactionLoggingService = require("./transactionLoggingService");
const logger = require("../utils/logger");
const ReportsModel = require("../models/reports.models");

class ReportExecutionService {
  constructor() {
    this.maxRetries = parseInt(process.env.REPORT_MAX_RETRIES) || 3;
    this.retryDelay = parseInt(process.env.REPORT_RETRY_DELAY) || 5000; // 5 seconds
  }

  /**
   * Execute a scheduled report with full logging and error handling
   */
  async executeReport(reportId, isManualTrigger = false) {
    const startTime = Date.now();
    let executionLogId = null;

    try {
      logger.info("Starting report execution", {
        reportId,
        isManualTrigger,
        timestamp: new Date().toISOString(),
      });

      // Get the scheduled report
      const report = await ReportScheduler.getScheduledReportById(reportId);

      if (!report) {
        throw new Error(`Scheduled report with ID ${reportId} not found`);
      }

      if (!report.is_active && !isManualTrigger) {
        throw new Error(`Scheduled report ${reportId} is not active`);
      }

      // Create execution log entry
      executionLogId = await ReportScheduler.createExecutionLog(reportId, {
        status: "pending",
        recipients: report.recipients,
        cc_recipients: report.cc_recipients,
        bcc_recipients: report.bcc_recipients,
      });

      logger.info("Execution log created", {
        reportId,
        executionLogId,
        reportName: report.report_name,
      });

      // Generate report data
      const reportData = await this.generateReportData(report);

      // Generate attachments (Excel only)
      const attachments = await this.generateReportAttachments(
        report,
        reportData
      );

      // Don't include attachment info in email body since files will be attached directly
      reportData.attachments = [];
      reportData.hasAttachments = false;
      reportData.attachmentNote =
        attachments.length > 0
          ? `Please find the detailed Excel report attached to this email as ${attachments
              .map((a) => a.fileName)
              .join(", ")}.`
          : "Report data is included in the email body above.";

      // Generate final email content with attachment data
      const emailContent = await this.generateEmailContent(report, reportData);

      // Store attachments in S3
      const storedAttachments =
        await transactionLoggingService.storeReportAttachments(
          executionLogId,
          attachments
        );

      // Send emails
      const emailResult = await this.sendReportEmails(
        report,
        emailContent,
        storedAttachments,
        executionLogId
      );

      // Calculate execution duration
      const executionDuration = Date.now() - startTime;

      // Update execution log with success (using only basic columns)
      await ReportScheduler.updateExecutionLog(executionLogId, {
        status: "success",
        emails_sent: emailResult.emailsSent,
        attachment_urls: storedAttachments.map((a) => a.url),
      });

      // Update next run time if not manual trigger
      if (!isManualTrigger) {
        const nextRun = ReportScheduler.calculateNextRun(report.schedule_time);
        await pool.query(
          "UPDATE scheduled_reports SET last_run = NOW(), next_run = ? WHERE id = ?",
          [nextRun, reportId]
        );
      }

      logger.info("Report execution completed successfully", {
        reportId,
        executionLogId,
        emailsSent: emailResult.emailsSent,
        executionDuration,
        attachmentCount: storedAttachments.length,
      });

      return {
        success: true,
        executionId: executionLogId,
        reportId,
        reportName: report.report_name,
        emailsSent: emailResult.emailsSent,
        attachments: storedAttachments.length,
        executionDuration,
      };
    } catch (error) {
      logger.error("Report execution failed", {
        reportId,
        executionLogId,
        error: error.message,
        stack: error.stack,
      });

      // Update execution log with failure (using only basic columns)
      if (executionLogId) {
        await ReportScheduler.updateExecutionLog(executionLogId, {
          status: "failed",
          error_message: error.message,
        });
      }

      throw error;
    }
  }

  /**
   * Generate report data based on report type
   */
  async generateReportData(report) {
    try {
      logger.info("Generating report data", {
        reportId: report.id,
        reportType: report.report_type,
        reportName: report.report_name,
      });

      // Parse filters from the report
      const filters = JSON.parse(report.filters || "{}");
      const dateRange = this.calculateDateRange(report.date_range);

      // Format date range for display
      const formattedDateRange = `${new Date(
        dateRange.fromDate
      ).toLocaleDateString()} - ${new Date(
        dateRange.toDate
      ).toLocaleDateString()}`;

      // Fetch real data based on report type
      const fetchedData = await this.fetchRealDataByType(
        report.report_type,
        report.user_id,
        filters,
        dateRange
      );

      const reportData = {
        reportName: report.report_name,
        reportType: report.report_type,
        dateRange: formattedDateRange, // Use formatted string instead of object
        dateRangeObject: dateRange, // Keep object for internal use
        filters,
        generatedAt: new Date().toISOString(),
        recipientName: "Valued Customer", // Could be personalized per recipient
        // Add real data based on report type
        ...fetchedData,
      };

      // Add common fields
      reportData.dashboardUrl = `${process.env.FRONTEND_URL}/dashboard`;
      reportData.nextReportDate = ReportScheduler.calculateNextRun(
        report.schedule_time
      ).toLocaleDateString();

      logger.info("Report data generated successfully", {
        reportId: report.id,
        dataKeys: Object.keys(reportData),
        dataSize: JSON.stringify(reportData).length,
      });

      return reportData;
    } catch (error) {
      logger.error("Error generating report data", {
        reportId: report.id,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Calculate date range based on report date range setting
   */
  calculateDateRange(dateRangeSetting) {
    const now = new Date();
    let fromDate, toDate;

    switch (dateRangeSetting) {
      case "last_7_days":
        fromDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        toDate = now;
        break;
      case "last_30_days":
        fromDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        toDate = now;
        break;
      case "last_90_days":
        fromDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        toDate = now;
        break;
      case "this_month":
        fromDate = new Date(now.getFullYear(), now.getMonth(), 1);
        toDate = now;
        break;
      case "last_month":
        fromDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        toDate = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      default:
        // Default to last 30 days
        fromDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        toDate = now;
    }

    return {
      fromDate: fromDate.toISOString().split("T")[0],
      toDate: toDate.toISOString().split("T")[0],
    };
  }

  /**
   * Fetch real data from APIs based on report type
   */
  async fetchRealDataByType(reportType, userId, filters, dateRange) {
    try {
      logger.info("Fetching real data for report", {
        reportType,
        userId,
        filters,
        dateRange,
      });

      switch (reportType) {
        case "Google Review Reports":
          return await this.fetchGoogleReviewData(userId, filters, dateRange);
        case "Performance Analytics":
          return await this.fetchPerformanceAnalyticsData(
            userId,
            filters,
            dateRange
          );
        case "Google Review Trends":
          return await this.fetchGoogleReviewTrendsData(
            userId,
            filters,
            dateRange
          );
        case "Performance Trends":
          return await this.fetchPerformanceTrendsData(
            userId,
            filters,
            dateRange
          );
        default:
          logger.warn("Unknown report type, using fallback data", {
            reportType,
          });
          return this.getFallbackData(reportType);
      }
    } catch (error) {
      logger.error("Error fetching real data, using fallback", {
        reportType,
        error: error.message,
      });
      return this.getFallbackData(reportType);
    }
  }

  /**
   * Generate email content using templates
   */
  async generateEmailContent(report, reportData) {
    try {
      logger.info("Generating email content", {
        reportType: report.report_type,
      });

      // Initialize template service if not already done
      await emailTemplateService.initialize();

      let htmlContent;
      if (report.report_type === "Google Review Reports") {
        htmlContent =
          await emailTemplateService.generateGoogleReviewReportEmail(
            reportData
          );
      } else if (report.report_type === "Performance Analytics") {
        htmlContent =
          await emailTemplateService.generatePerformanceAnalyticsEmail(
            reportData
          );
      }

      const emailContent = {
        subject: `MyLocobiz - ${report.report_type} - ${report.report_name}`,
        htmlBody: htmlContent,
        textBody: this.generateTextVersion(reportData),
      };

      logger.info("Email content generated successfully", {
        reportType: report.report_type,
        htmlSize: htmlContent.length,
      });

      return emailContent;
    } catch (error) {
      logger.error("Error generating email content:", error);
      throw error;
    }
  }

  /**
   * Generate report attachments (Excel only)
   */
  async generateReportAttachments(report, reportData) {
    try {
      logger.info("Generating report attachments", {
        reportType: report.report_type,
      });

      const attachments = [];

      // HTML content not needed since PDF generation is disabled

      // Generate complete report package
      const reportPackage =
        await reportGenerationService.generateCompleteReportPackage(
          reportData,
          report.report_type
        );

      // Convert S3 results to attachment format and download file buffers
      if (reportPackage.excel) {
        try {
          // Download the file buffer from S3
          const excelBuffer = await this.downloadFileFromS3(
            reportPackage.excel.url
          );

          attachments.push({
            fileName: reportPackage.excel.fileName,
            fileBuffer: excelBuffer,
            contentType:
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            description: "Detailed Excel report with data and charts",
            url: reportPackage.excel.url,
          });
        } catch (error) {
          logger.error("Failed to download Excel file from S3:", error);
          // Still add the attachment but with empty buffer as fallback
          attachments.push({
            fileName: reportPackage.excel.fileName,
            fileBuffer: Buffer.from(""),
            contentType:
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            description: "Detailed Excel report with data and charts",
            url: reportPackage.excel.url,
          });
        }
      }

      // PDF attachment removed - only Excel files are sent with scheduled reports

      logger.info("Report attachments generated successfully", {
        reportType: report.report_type,
        attachmentCount: attachments.length,
      });

      return attachments;
    } catch (error) {
      logger.error("Error generating report attachments:", error);
      throw error;
    }
  }

  /**
   * Send report emails to all recipients
   */
  async sendReportEmails(report, emailContent, attachments, executionLogId) {
    try {
      logger.info("Sending report emails", {
        toRecipients: report.recipients.length,
        ccRecipients: report.cc_recipients.length,
        bccRecipients: report.bcc_recipients.length,
        attachments: attachments.length,
      });

      // Use the original HTML body (attachments are handled by the template)
      const enhancedHtmlBody = emailContent.htmlBody;

      // Download files from S3 and prepare email attachments
      const emailAttachments = [];
      for (const attachment of attachments) {
        try {
          logger.info("Downloading attachment from S3", {
            fileName: attachment.fileName,
            url: attachment.url,
          });

          // Download file content from S3
          const fileBuffer = await this.downloadFileFromS3(attachment.url);

          // Validate the downloaded buffer
          if (
            !fileBuffer ||
            !Buffer.isBuffer(fileBuffer) ||
            fileBuffer.length === 0
          ) {
            logger.error("Invalid or empty file buffer from S3", {
              fileName: attachment.fileName,
              url: attachment.url,
              bufferType: typeof fileBuffer,
              isBuffer: Buffer.isBuffer(fileBuffer),
              size: fileBuffer ? fileBuffer.length : 0,
            });
            continue; // Skip this attachment
          }

          emailAttachments.push({
            filename: attachment.fileName,
            content: fileBuffer,
            contentType:
              attachment.contentType ||
              this.getContentTypeFromFileName(attachment.fileName),
          });

          logger.info("Successfully prepared attachment", {
            fileName: attachment.fileName,
            size: fileBuffer.length,
          });
        } catch (error) {
          logger.error("Failed to download attachment from S3", {
            fileName: attachment.fileName,
            url: attachment.url,
            error: error.message,
          });
          // Continue with other attachments even if one fails
        }
      }

      logger.info("Prepared email attachments", {
        totalAttachments: emailAttachments.length,
        attachmentSizes: emailAttachments.map((att) => ({
          name: att.filename,
          size: att.content.length,
          type: att.contentType,
        })),
      });

      // Send email with attachments
      const emailResult = await emailService.sendEmail({
        to: report.recipients,
        cc: report.cc_recipients,
        bcc: report.bcc_recipients,
        subject: emailContent.subject,
        htmlBody: enhancedHtmlBody,
        textBody: emailContent.textBody,
        attachments: emailAttachments, // Include actual file attachments
      });

      if (emailResult.success) {
        // Log individual email transactions
        const emailTransactions = [];

        // Log TO recipients
        for (const recipient of report.recipients) {
          emailTransactions.push({
            recipientEmail: recipient,
            recipientType: "to",
            emailStatus: "sent",
            awsSesMessageId: emailResult.messageIds[0], // Simplified - in reality, track per recipient
          });
        }

        // Log CC recipients
        for (const recipient of report.cc_recipients) {
          emailTransactions.push({
            recipientEmail: recipient,
            recipientType: "cc",
            emailStatus: "sent",
            awsSesMessageId: emailResult.messageIds[0],
          });
        }

        // Log BCC recipients
        for (const recipient of report.bcc_recipients) {
          emailTransactions.push({
            recipientEmail: recipient,
            recipientType: "bcc",
            emailStatus: "sent",
            awsSesMessageId: emailResult.messageIds[0],
          });
        }

        // Batch log email transactions
        await transactionLoggingService.logEmailTransactionsBatch(
          executionLogId,
          emailTransactions
        );

        logger.info("Report emails sent successfully", {
          emailsSent: emailResult.emailsSent,
          messageIds: emailResult.messageIds,
        });

        return emailResult;
      } else {
        throw new Error(emailResult.error || "Failed to send emails");
      }
    } catch (error) {
      logger.error("Error sending report emails:", error);
      throw error;
    }
  }

  /**
   * Add attachment download links to email content
   */
  addAttachmentLinksToEmail(htmlBody, attachments) {
    if (!attachments || attachments.length === 0) {
      return htmlBody;
    }

    const attachmentSection = `
      <div style="background-color: #e8f5e8; border: 1px solid #c3e6c3; border-radius: 8px; padding: 20px; margin: 30px 0;">
        <div style="font-size: 18px; font-weight: bold; color: #155724; margin-bottom: 15px;">📎 Report Attachments</div>
        ${attachments
          .map(
            (att) => `
          <div style="margin-bottom: 10px; padding: 10px; background-color: white; border-radius: 6px; border: 1px solid #c3e6c3;">
            <div style="display: flex; align-items: center;">
              <div style="margin-right: 10px; color: #309898;">📄</div>
              <div style="flex: 1;">
                <div style="font-weight: 600; color: #155724;">
                  <a href="${att.url}" style="color: #155724; text-decoration: none;">${att.name}</a>
                </div>
                <div style="font-size: 12px; color: #666;">${att.description}</div>
              </div>
            </div>
          </div>
        `
          )
          .join("")}
        <div style="margin-top: 15px; padding: 10px; background-color: #d1ecf1; border-radius: 6px; font-size: 14px;">
          <strong>Note:</strong> Click on the attachment names above to download the detailed reports.
        </div>
      </div>
    `;

    // Insert before the closing body tag
    return htmlBody.replace("</body>", attachmentSection + "</body>");
  }

  /**
   * Generate simple text version of the email
   */
  generateTextVersion(reportData) {
    return `
${reportData.reportType} - ${reportData.reportName}

Report Period: ${reportData.dateRange}
Generated On: ${new Date().toLocaleDateString()}

This is an automated report from MyLocobiz Report Scheduler.
Please view the HTML version of this email for the complete report with charts and detailed information.

Detailed reports are also available as downloadable attachments.

Best regards,
MyLocobiz Team
    `.trim();
  }

  /**
   * Format file size in human readable format
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Fetch Google Review Reports data
   */
  async fetchGoogleReviewData(userId, filters, dateRange) {
    try {
      const requestData = {
        businessId: filters.businessId || null,
        accountId: filters.accountId || null,
        locationId: filters.locationId || null,
        startDate: dateRange.fromDate,
        endDate: dateRange.toDate,
      };

      const reviewsData = await ReportsModel.fetchReviewsData(
        userId,
        requestData
      );
      const aggregated = ReportsModel.generateAggregatedData(reviewsData);

      // Derive names for Business / Account / Locations from dataset
      const businessNames = [
        ...new Set(reviewsData.map((r) => r.businessName).filter(Boolean)),
      ];
      const accountNames = [
        ...new Set(reviewsData.map((r) => r.accountName).filter(Boolean)),
      ];
      const locationNames = [
        ...new Set(
          reviewsData
            .map((r) => r.gmbLocationName || r.locationName)
            .filter(Boolean)
        ),
      ];

      const businessLabel =
        businessNames.length <= 1 ? businessNames[0] || "All" : "Multiple";
      const accountLabel =
        accountNames.length <= 1 ? accountNames[0] || "All" : "Multiple";
      const locationsLabel =
        locationNames.length <= 1 ? locationNames[0] || "All" : "Multiple";

      return {
        // Names to display in Summary sheet
        businessName: businessLabel,
        accountName: accountLabel,
        locationsLabel,

        // Totals and summary metrics
        totalReviews: aggregated.reviewVolume
          ? Object.values(aggregated.reviewVolume).reduce((a, b) => a + b, 0)
          : 0,
        averageRating: this.calculateAverageRating(
          aggregated.ratingDistribution
        ),
        newReviews: reviewsData.filter(
          (r) => new Date(r.createTime) >= new Date(dateRange.fromDate)
        ).length,
        responseRate: aggregated.responseRate
          ? parseFloat(
              (
                (aggregated.responseRate.replied /
                  Math.max(aggregated.responseRate.total, 1)) *
                100
              ).toFixed(1)
            )
          : 0,
        averageResponseTime: this.calculateAverageResponseTime(
          aggregated.avgResponseTime
        ),

        // Aggregated maps (to feed Excel sheet creators)
        ratingsVsMonth: aggregated.ratingsVsMonth,
        reviewsVsReplies: aggregated.reviewsVsReplies,
        ratingDistribution: this.formatRatingDistribution(
          aggregated.ratingDistribution
        ),
        reviewVolume: aggregated.reviewVolume,

        // Detail and location views
        reviews: this.formatRecentReviews(reviewsData),
        recentReviews: this.formatRecentReviews(reviewsData.slice(0, 5)),
        locationPerformance: this.formatLocationPerformance(reviewsData),
        insights: this.generateReviewInsights({ aggregated }),
      };
    } catch (error) {
      logger.error("Error fetching Google Review data:", error);
      return this.getFallbackData("Google Review Reports");
    }
  }

  /**
   * Fetch Performance Analytics data
   */
  async fetchPerformanceAnalyticsData(userId, filters, dateRange) {
    try {
      const LocationAnalytics = require("../models/locationMetrics.models");

      // Get location IDs based on filters
      let locationIds = [];
      if (filters.locationId) {
        locationIds = [filters.locationId];
      } else {
        // If no specific location, get all locations for the user by joining with gmb_locations
        const pool = require("../config/db");
        const query = `
          SELECT DISTINCT gl.gmbLocationId
          FROM gmb_oauth_tokens gat
          INNER JOIN gmb_locations gl ON gl.gmbAccountId = gat.gmbAccountId
          WHERE gat.userId = ? AND gat.statusId = 1 AND gl.statusId = 1
        `;
        const rows = await pool.query(query, [userId]);
        locationIds = rows.map((row) => row.gmbLocationId);
      }

      if (locationIds.length === 0) {
        logger.warn("No locations found for Performance Analytics", {
          userId,
          filters,
        });
        return this.getFallbackData("Performance Analytics");
      }

      logger.info("Fetching Performance Analytics data", {
        userId,
        locationIds,
        dateRange,
      });

      // Use the same method as the frontend API
      const dbData = await LocationAnalytics.getFormattedAnalyticsData(
        locationIds,
        dateRange.fromDate,
        dateRange.toDate
      );

      // Aggregate data from all locations (same logic as frontend)
      const aggregatedData = this.aggregateMultiLocationData(
        dbData,
        locationIds
      );

      // Process the data to match frontend structure
      const processedData = this.processPerformanceAnalyticsData(
        aggregatedData,
        dateRange
      );

      return processedData;
    } catch (error) {
      logger.error("Error fetching Performance Analytics data:", error);
      return this.getFallbackData("Performance Analytics");
    }
  }

  /**
   * Fetch Google Review Trends data
   */
  async fetchGoogleReviewTrendsData(userId, filters, dateRange) {
    try {
      const requestData = {
        businessId: filters.businessId || null,
        accountId: filters.accountId || null,
        locationId: filters.locationId || null,
        fromDate: dateRange.fromDate,
        toDate: dateRange.toDate,
      };

      const performanceData = await ReportsModel.fetchPerformanceData(
        userId,
        requestData
      );
      const analytics = ReportsModel.generatePerformanceData(performanceData);

      return {
        trends: analytics.trends,
        charts: analytics.charts,
        currentPeriod: analytics.currentPeriod,
        comparisonPeriod: analytics.comparisonPeriod,
        insights: this.generateTrendInsights(analytics.trends),
      };
    } catch (error) {
      logger.error("Error fetching Google Review Trends data:", error);
      return this.getFallbackData("Google Review Trends");
    }
  }

  /**
   * Fetch Performance Trends data
   */
  async fetchPerformanceTrendsData(userId, filters, dateRange) {
    try {
      const requestData = {
        businessId: filters.businessId || null,
        accountId: filters.accountId || null,
        locationIds: filters.locationId ? [filters.locationId] : [],
        fromDate: dateRange.fromDate,
        toDate: dateRange.toDate,
      };

      const analyticsData = await ReportsModel.fetchGoogleAnalyticsData(
        userId,
        requestData
      );
      const chartData = ReportsModel.generateAnalyticsCharts(
        analyticsData,
        dateRange.fromDate,
        dateRange.toDate
      );

      return {
        chartData,
        trends: this.calculateTrendsFromCharts(chartData),
        insights: this.generateAnalyticsInsights(chartData),
      };
    } catch (error) {
      logger.error("Error fetching Performance Trends data:", error);
      return this.getFallbackData("Performance Trends");
    }
  }

  /**
   * Helper methods for data formatting
   */
  calculateAverageRating(ratingDistribution) {
    if (!ratingDistribution || Object.keys(ratingDistribution).length === 0)
      return 0;

    let totalRatings = 0;
    let totalCount = 0;

    Object.entries(ratingDistribution).forEach(([rating, count]) => {
      totalRatings += parseInt(rating) * count;
      totalCount += count;
    });

    return totalCount > 0
      ? parseFloat((totalRatings / totalCount).toFixed(1))
      : 0;
  }

  calculateAverageResponseTime(responseTimes) {
    if (!responseTimes || responseTimes.length === 0) return "N/A";
    const avgHours =
      responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    return `${avgHours.toFixed(1)} hours`;
  }

  formatRatingDistribution(ratingDistribution) {
    if (!ratingDistribution) return [];

    const total = Object.values(ratingDistribution).reduce((a, b) => a + b, 0);
    return Object.entries(ratingDistribution)
      .map(([rating, count]) => ({
        rating: parseInt(rating),
        count,
        percentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0,
      }))
      .sort((a, b) => b.rating - a.rating);
  }

  formatRecentReviews(reviews) {
    return reviews.map((review) => ({
      date: new Date(review.createTime).toLocaleDateString(),
      rating: review.starRating || review.StarRatingInt || 0,
      locationName:
        review.locationName || review.gmbLocationName || "Unknown Location",
      text: review.comment || review.review || "",
      reviewerName: review.reviewerName || review.reviewer?.displayName || "",
      responseText:
        review.reviewReplyComment || review.reviewReply?.comment || "",
      responseDate:
        review.reviewReplyUpdateTime || review.reviewReply?.updateTime || "",
      hasResponse:
        !!review.reviewReply ||
        !!review.reviewReplyComment ||
        (typeof review.hasReply === "number" ? review.hasReply === 1 : false),
    }));
  }

  formatLocationPerformance(reviews) {
    const locationStats = {};

    reviews.forEach((review) => {
      const locationName = review.locationName || "Unknown Location";
      if (!locationStats[locationName]) {
        locationStats[locationName] = {
          name: locationName,
          totalReviews: 0,
          totalRating: 0,
          responses: 0,
          latestReviewDate: null,
        };
      }

      locationStats[locationName].totalReviews++;
      locationStats[locationName].totalRating += review.starRating;
      if (review.reviewReply) locationStats[locationName].responses++;

      const reviewDate = new Date(review.createTime);
      if (
        !locationStats[locationName].latestReviewDate ||
        reviewDate > new Date(locationStats[locationName].latestReviewDate)
      ) {
        locationStats[locationName].latestReviewDate = review.createTime;
      }
    });

    return Object.values(locationStats).map((location) => ({
      name: location.name,
      totalReviews: location.totalReviews,
      averageRating: (location.totalRating / location.totalReviews).toFixed(1),
      responseRate: (
        (location.responses / location.totalReviews) *
        100
      ).toFixed(0),
      latestReviewDate: new Date(
        location.latestReviewDate
      ).toLocaleDateString(),
    }));
  }

  generateReviewInsights(aggregatedData) {
    const insights = [];

    if (aggregatedData.aggregated.responseRate) {
      const responseRate =
        (aggregatedData.aggregated.responseRate.replied /
          aggregatedData.aggregated.responseRate.total) *
        100;
      if (responseRate > 80) {
        insights.push({
          title: "Excellent Response Rate",
          description: `You're responding to ${responseRate.toFixed(
            1
          )}% of reviews - keep up the great work!`,
        });
      } else if (responseRate < 50) {
        insights.push({
          title: "Improve Response Rate",
          description: `Consider responding to more reviews to improve customer engagement (current: ${responseRate.toFixed(
            1
          )}%).`,
        });
      }
    }

    return insights;
  }

  generatePerformanceInsights(trends) {
    const insights = [];

    if (trends.impressions && trends.impressions.change > 10) {
      insights.push({
        icon: "📈",
        title: "Growing Visibility",
        description: `Your impressions increased by ${trends.impressions.change.toFixed(
          1
        )}% this period.`,
      });
    }

    if (trends.clicks && trends.clicks.change > 15) {
      insights.push({
        icon: "🎯",
        title: "Strong Engagement",
        description: `Clicks increased by ${trends.clicks.change.toFixed(
          1
        )}% - your content is resonating!`,
      });
    }

    return insights;
  }

  formatSearchPerformance(searchTypeBreakdown) {
    if (!searchTypeBreakdown) return [];

    return Object.entries(searchTypeBreakdown).map(([type, data]) => ({
      type: type.charAt(0).toUpperCase() + type.slice(1),
      impressions: data.impressions || 0,
      clicks: data.clicks || 0,
      ctr:
        data.impressions > 0
          ? ((data.clicks / data.impressions) * 100).toFixed(1)
          : 0,
    }));
  }

  formatTopLocations(performanceData) {
    // Group by location and calculate metrics
    const locationStats = {};

    performanceData.forEach((item) => {
      const locationName = item.locationName || "Unknown Location";
      if (!locationStats[locationName]) {
        locationStats[locationName] = {
          name: locationName,
          impressions: 0,
          clicks: 0,
          actions: 0,
        };
      }

      locationStats[locationName].impressions += item.impressions || 0;
      locationStats[locationName].clicks += item.clicks || 0;
      locationStats[locationName].actions += item.actions || 0;
    });

    return Object.values(locationStats)
      .map((location) => ({
        name: location.name,
        impressions: location.impressions,
        clicks: location.clicks,
        ctr:
          location.impressions > 0
            ? ((location.clicks / location.impressions) * 100).toFixed(1)
            : 0,
        actions: location.actions,
        score: Math.min(
          100,
          Math.round(
            (location.clicks / Math.max(location.impressions, 1)) * 1000
          )
        ),
      }))
      .sort((a, b) => b.score - a.score);
  }

  generateTrendInsights(trends) {
    const insights = [];

    Object.entries(trends).forEach(([metric, data]) => {
      if (data.change > 20) {
        insights.push({
          title: `${metric.charAt(0).toUpperCase() + metric.slice(1)} Surge`,
          description: `${metric} increased by ${data.change.toFixed(
            1
          )}% - excellent growth!`,
        });
      } else if (data.change < -20) {
        insights.push({
          title: `${metric.charAt(0).toUpperCase() + metric.slice(1)} Decline`,
          description: `${metric} decreased by ${Math.abs(data.change).toFixed(
            1
          )}% - consider optimization strategies.`,
        });
      }
    });

    return insights;
  }

  calculateTrendsFromCharts(chartData) {
    // Calculate trends from chart data
    const trends = {};

    Object.entries(chartData).forEach(([chartType, data]) => {
      if (Array.isArray(data) && data.length > 1) {
        const firstValue = data[0].value || 0;
        const lastValue = data[data.length - 1].value || 0;
        const change =
          firstValue > 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;

        trends[chartType] = {
          current: lastValue,
          previous: firstValue,
          change: change,
        };
      }
    });

    return trends;
  }

  generateAnalyticsInsights(chartData) {
    const insights = [];

    // Analyze chart data for insights
    Object.entries(chartData).forEach(([chartType, data]) => {
      if (Array.isArray(data) && data.length > 0) {
        const totalValue = data.reduce(
          (sum, item) => sum + (item.value || 0),
          0
        );
        const avgValue = totalValue / data.length;

        if (avgValue > 100) {
          // Arbitrary threshold
          insights.push({
            title: `Strong ${chartType.replace(/([A-Z])/g, " $1").trim()}`,
            description: `Your ${chartType} metrics show consistent performance with an average of ${avgValue.toFixed(
              0
            )}.`,
          });
        }
      }
    });

    return insights;
  }

  /**
   * Aggregate multi-location data (same logic as frontend)
   */
  aggregateMultiLocationData(dbData, locationIds) {
    // The dbData already contains aggregated data from the API
    // Find the first location that has data
    for (const locationId of locationIds) {
      if (dbData[locationId] && dbData[locationId].multiDailyMetricTimeSeries) {
        return dbData[locationId];
      }
    }

    // Return empty structure if no data found
    return {
      multiDailyMetricTimeSeries: [],
    };
  }

  /**
   * Process Performance Analytics data to match frontend structure
   */
  processPerformanceAnalyticsData(aggregatedData, dateRange) {
    try {
      if (
        !aggregatedData.multiDailyMetricTimeSeries ||
        aggregatedData.multiDailyMetricTimeSeries.length === 0
      ) {
        return this.getFallbackData("Performance Analytics");
      }

      const daysDifference = this.getDaysDifference(
        dateRange.fromDate,
        dateRange.toDate
      );
      const isSameMonthYear = this.isSameMonthYear(
        dateRange.fromDate,
        dateRange.toDate
      );

      // Aggregate different metrics using the same logic as frontend
      const websiteData = this.aggregateMetricData(
        [aggregatedData],
        "WEBSITE_CLICKS",
        daysDifference,
        isSameMonthYear
      );

      const callData = this.aggregateMetricData(
        [aggregatedData],
        "CALL_CLICKS",
        daysDifference,
        isSameMonthYear
      );

      const directionsData = this.aggregateMetricData(
        [aggregatedData],
        "BUSINESS_DIRECTION_REQUESTS",
        daysDifference,
        isSameMonthYear
      );

      const messagingClicks = this.aggregateMetricData(
        [aggregatedData],
        "BUSINESS_CONVERSATIONS",
        daysDifference,
        isSameMonthYear
      );

      const bookings = this.aggregateMetricData(
        [aggregatedData],
        "BUSINESS_FOOD_ORDERS",
        daysDifference,
        isSameMonthYear
      );

      // Calculate totals
      const totalCalls = callData.data.reduce((sum, val) => sum + val, 0);
      const totalDirections = directionsData.data.reduce(
        (sum, val) => sum + val,
        0
      );
      const totalWebsiteClicks = websiteData.data.reduce(
        (sum, val) => sum + val,
        0
      );
      const totalMessaging = messagingClicks.data.reduce(
        (sum, val) => sum + val,
        0
      );
      const totalBookings = bookings.data.reduce((sum, val) => sum + val, 0);

      const totalInteractions =
        totalCalls + totalDirections + totalWebsiteClicks;

      // Calculate location count and data points
      const locationCount = aggregatedData.multiDailyMetricTimeSeries
        ? aggregatedData.multiDailyMetricTimeSeries.length
        : 0;

      const totalDataPoints = [
        websiteData.data,
        callData.data,
        directionsData.data,
        messagingClicks.data,
        bookings.data,
      ].reduce(
        (total, dataArray) => total + dataArray.filter((val) => val > 0).length,
        0
      );

      return {
        // Location and data info
        locationCount,
        totalDataPoints,
        locations: Array.from({ length: locationCount }, (_, i) => ({
          id: i + 1,
          name: `Location ${i + 1}`,
        })),

        // Summary metrics
        totalInteractions,
        totalCalls,
        totalDirections,
        totalWebsiteClicks,
        totalMessaging,
        totalBookings,

        // Chart data (same structure as frontend)
        websiteData,
        callData,
        directionsData,
        messagingClicks,
        bookings,

        // Additional metrics for compatibility
        totalImpressions: 0, // Not available in this data structure
        totalClicks: totalInteractions,
        clickThroughRate: 0,
        totalActions: totalInteractions,
        websiteClicks: totalWebsiteClicks,
        phoneClicks: totalCalls,
        directionClicks: totalDirections,
        photoViews: 0,

        // Insights
        insights: this.generatePerformanceInsights({
          calls: { current: totalCalls, change: 0 },
          directions: { current: totalDirections, change: 0 },
          website: { current: totalWebsiteClicks, change: 0 },
        }),
      };
    } catch (error) {
      logger.error("Error processing Performance Analytics data:", error);
      return this.getFallbackData("Performance Analytics");
    }
  }

  /**
   * Aggregate metric data (same logic as frontend)
   */
  aggregateMetricData(
    allLocationData,
    metric,
    daysDifference,
    isSameMonthYear
  ) {
    const VALIDATION_DAYS = 45;
    const aggregatedDaily = {};
    const aggregatedMonthly = {};

    // Process each location's data
    allLocationData.forEach((locationData) => {
      if (!locationData || !locationData.multiDailyMetricTimeSeries) return;

      // Handle the nested structure properly
      locationData.multiDailyMetricTimeSeries.forEach((multiMetric) => {
        if (!multiMetric || !multiMetric.dailyMetricTimeSeries) return;

        const metricSeries = multiMetric.dailyMetricTimeSeries.find(
          (series) => series && series.dailyMetric === metric
        );

        if (
          !metricSeries ||
          !metricSeries.timeSeries ||
          !metricSeries.timeSeries.datedValues
        )
          return;

        // Aggregate values for this location
        for (const entry of metricSeries.timeSeries.datedValues) {
          if (!entry.date) continue;

          const { year, month, day } = entry.date;
          const dayjs = require("dayjs");
          const key =
            daysDifference < VALIDATION_DAYS || isSameMonthYear
              ? dayjs(`${year}-${month}-${day}`).format("YYYY-MMM-DD")
              : dayjs(`${year}-${month}-01`).format("MMM YYYY");

          const value = parseInt(entry.value ?? "0", 10);
          if (daysDifference < VALIDATION_DAYS || isSameMonthYear) {
            aggregatedDaily[key] = (aggregatedDaily[key] || 0) + value;
          } else {
            aggregatedMonthly[key] = (aggregatedMonthly[key] || 0) + value;
          }
        }
      });
    });

    const labels =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? Object.keys(aggregatedDaily).sort()
        : Object.keys(aggregatedMonthly);
    const data =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? labels.map((label) => aggregatedDaily[label] || 0)
        : labels.map((label) => aggregatedMonthly[label] || 0);

    return { data, labels };
  }

  /**
   * Calculate days difference
   */
  getDaysDifference(fromDate, toDate) {
    const dayjs = require("dayjs");
    return dayjs(toDate).diff(dayjs(fromDate), "day");
  }

  /**
   * Check if dates are in same month and year
   */
  isSameMonthYear(fromDate, toDate) {
    const dayjs = require("dayjs");
    const from = dayjs(fromDate);
    const to = dayjs(toDate);
    return from.year() === to.year() && from.month() === to.month();
  }

  getFallbackData(reportType) {
    // Return mock data as fallback when real data fails
    switch (reportType) {
      case "Google Review Reports":
        return {
          totalReviews: 0,
          averageRating: 0,
          newReviews: 0,
          responseRate: 0,
          averageResponseTime: "N/A",
          ratingDistribution: [],
          recentReviews: [],
          locationPerformance: [],
          insights: [
            {
              title: "No Data",
              description: "No review data available for this period.",
            },
          ],
        };
      case "Performance Analytics":
        return {
          totalInteractions: 0,
          totalCalls: 0,
          totalDirections: 0,
          totalWebsiteClicks: 0,
          totalMessaging: 0,
          totalBookings: 0,
          websiteData: { data: [], labels: [] },
          callData: { data: [], labels: [] },
          directionsData: { data: [], labels: [] },
          messagingClicks: { data: [], labels: [] },
          bookings: { data: [], labels: [] },
          totalImpressions: 0,
          totalClicks: 0,
          clickThroughRate: 0,
          totalActions: 0,
          websiteClicks: 0,
          phoneClicks: 0,
          directionClicks: 0,
          photoViews: 0,
          insights: [
            {
              title: "No Data",
              description: "No performance data available for this period.",
            },
          ],
        };
      default:
        return {
          insights: [
            {
              title: "No Data",
              description: "No data available for this report type.",
            },
          ],
        };
    }
  }

  /**
   * Download file from S3 URL
   */
  async downloadFileFromS3(s3Url) {
    try {
      const AWS = require("aws-sdk");

      // Configure AWS S3
      const s3 = new AWS.S3({
        accessKeyId: process.env.APP_AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.APP_AWS_SECRET_ACCESS_KEY,
        region: process.env.APP_AWS_REGION,
      });

      logger.info("Parsing S3 URL", { url: s3Url });

      // Parse S3 URL to get bucket and key
      let bucketName, key;

      if (s3Url.includes("amazonaws.com")) {
        // Format: https://bucket-name.s3.region.amazonaws.com/key/path
        const urlParts = s3Url.replace("https://", "").split("/");
        bucketName = urlParts[0].split(".")[0]; // Extract bucket name from domain
        key = urlParts.slice(1).join("/"); // Everything after the first slash
      } else if (s3Url.includes("s3://")) {
        // Format: s3://bucket-name/key/path
        const s3Parts = s3Url.replace("s3://", "").split("/");
        bucketName = s3Parts[0];
        key = s3Parts.slice(1).join("/");
      } else {
        throw new Error(`Unsupported S3 URL format: ${s3Url}`);
      }

      if (!bucketName || !key) {
        throw new Error(
          `Failed to parse S3 URL: bucket=${bucketName}, key=${key}`
        );
      }

      logger.info("Downloading file from S3", {
        bucket: bucketName,
        key: key,
        url: s3Url,
      });

      // Download file from S3
      const params = {
        Bucket: bucketName,
        Key: key,
      };

      const data = await s3.getObject(params).promise();

      if (!data.Body) {
        throw new Error("S3 response contains no body data");
      }

      logger.info("Successfully downloaded file from S3", {
        bucket: bucketName,
        key: key,
        size: data.Body.length,
        contentType: data.ContentType,
      });

      return data.Body;
    } catch (error) {
      logger.error("Error downloading file from S3", {
        url: s3Url,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get content type from file name
   */
  getContentTypeFromFileName(fileName) {
    const extension = fileName.toLowerCase().split(".").pop();

    const contentTypes = {
      pdf: "application/pdf",
      xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      xls: "application/vnd.ms-excel",
      csv: "text/csv",
      txt: "text/plain",
      html: "text/html",
      json: "application/json",
      zip: "application/zip",
      png: "image/png",
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      gif: "image/gif",
    };

    return contentTypes[extension] || "application/octet-stream";
  }
}

module.exports = new ReportExecutionService();
