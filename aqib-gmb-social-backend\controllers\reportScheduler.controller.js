const ReportScheduler = require("../models/reportScheduler.models");
const emailService = require("../services/emailService");
const reportExecutionService = require("../services/reportExecutionService");
const transactionLoggingService = require("../services/transactionLoggingService");
const logger = require("../utils/logger");

/**
 * Welcome endpoint for Report Scheduler
 */
const welcome = async (req, res) => {
  try {
    logger.logControllerAction("reportScheduler", "welcome", req.requestId);

    const response = {
      message: "Report Scheduler API",
      version: "1.0.0",
    };

    logger.info("Report Scheduler welcome endpoint accessed", {
      requestId: req.requestId,
    });

    res.status(200).json(response);
  } catch (error) {
    logger.error("Error in report scheduler welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Get all scheduled reports for the authenticated user
 */
const getScheduledReports = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "getScheduledReports",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await ReportScheduler.getScheduledReports(
      userId,
      page,
      limit
    );

    logger.info("Scheduled reports retrieved successfully", {
      requestId: req.requestId,
      userId,
      count: result.data.length,
    });

    res.status(200).json({
      success: true,
      message: "Scheduled reports retrieved successfully",
      data: result.data,
      totalRecords: result.totalRecords,
      page,
      limit,
    });
  } catch (error) {
    logger.error("Error getting scheduled reports", {
      requestId: req.requestId,
      userId: req.user?.userId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve scheduled reports",
      error: error.message,
    });
  }
};

/**
 * Create a new scheduled report
 */
const createScheduledReport = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "createScheduledReport",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);
    const reportData = {
      ...req.body,
      userId,
      createdBy: userId,
    };

    // Validate required fields
    const requiredFields = [
      "reportType",
      "reportName",
      "scheduleTime",
      "dateRange",
      "recipients",
    ];
    for (const field of requiredFields) {
      if (!reportData[field]) {
        return res.status(400).json({
          success: false,
          message: `Missing required field: ${field}`,
        });
      }
    }

    // Validate recipients array
    if (
      !Array.isArray(reportData.recipients) ||
      reportData.recipients.length === 0
    ) {
      return res.status(400).json({
        success: false,
        message: "At least one recipient email is required",
      });
    }

    // Validate selected locations array
    if (
      !Array.isArray(reportData.selectedLocations) ||
      reportData.selectedLocations.length === 0
    ) {
      return res.status(400).json({
        success: false,
        message: "At least one location must be selected",
      });
    }

    // Create scheduled reports for each selected location
    const createdReports = [];
    const errors = [];

    for (const location of reportData.selectedLocations) {
      try {
        const locationReportData = {
          ...reportData,
          businessId: location.businessId || null,
          accountId: location.accountId || null,
          locationId: location.locationId || null,
          reportName: `${reportData.reportName}`,
          // Remove selectedLocations from the data sent to model
          selectedLocations: undefined,
        };

        logger.info(`Creating report for location: ${location.locationName}`);
        console.log(
          "Location report data:",
          JSON.stringify(locationReportData, null, 2)
        );

        try {
          console.log("BEFORE calling createScheduledReport");
          const result = await ReportScheduler.createScheduledReport(
            locationReportData
          );
          console.log(
            "AFTER calling createScheduledReport - Model result:",
            result
          );
          console.log("Result type:", typeof result);
          console.log("Result has id:", result && result.id);

          if (result && result.id) {
            console.log("SUCCESS: Adding to createdReports");
            createdReports.push({
              reportId: result.id,
              locationId: location.locationId,
              locationName: location.locationName,
            });
          } else {
            console.log("FAILURE: Adding to errors - no ID");
            errors.push({
              locationId: location.locationId,
              locationName: location.locationName,
              error: "Failed to create report - no ID returned",
            });
          }
        } catch (modelError) {
          console.log("EXCEPTION in createScheduledReport:", modelError);
          console.log("Exception message:", modelError.message);
          console.log("Exception stack:", modelError.stack);
          throw modelError; // Re-throw to be caught by outer catch
        }
      } catch (locationError) {
        logger.error(
          `Error creating report for location ${location.locationId}:`,
          locationError
        );
        errors.push({
          locationId: location.locationId,
          locationName: location.locationName,
          error: locationError.message,
        });
      }
    }

    // Prepare response
    const response = {
      success: createdReports.length > 0,
      message: `Successfully created ${createdReports.length} scheduled reports out of ${reportData.selectedLocations.length} selected locations`,
      data: {
        totalLocations: reportData.selectedLocations.length,
        successfulReports: createdReports.length,
        failedReports: errors.length,
        createdReports,
        errors: errors.length > 0 ? errors : undefined,
      },
    };

    if (createdReports.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Failed to create any scheduled reports",
        data: response.data,
      });
    }

    logger.info("Scheduled reports created successfully", {
      requestId: req.requestId,
      userId,
      totalReports: createdReports.length,
      reportName: reportData.reportName,
    });

    return res.status(201).json(response);
  } catch (error) {
    logger.error("Error creating scheduled report", {
      requestId: req.requestId,
      userId: req.user?.userId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to create scheduled report",
      error: error.message,
    });
  }
};

/**
 * Update an existing scheduled report
 */
const updateScheduledReport = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "updateScheduledReport",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);
    const reportId = parseInt(req.params.id);
    const updateData = {
      ...req.body,
      updatedBy: userId,
      updatedAt: new Date(),
    };

    if (!reportId) {
      return res.status(400).json({
        success: false,
        message: "Report ID is required",
      });
    }

    const result = await ReportScheduler.updateScheduledReport(
      reportId,
      updateData,
      userId
    );

    if (!result) {
      return res.status(404).json({
        success: false,
        message: "Scheduled report not found or not authorized to update",
      });
    }

    logger.info("Scheduled report updated successfully", {
      requestId: req.requestId,
      userId,
      reportId,
    });

    res.status(200).json({
      success: true,
      message: "Scheduled report updated successfully",
      data: result,
    });
  } catch (error) {
    logger.error("Error updating scheduled report", {
      requestId: req.requestId,
      userId: req.user?.userId,
      reportId: req.params.id,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to update scheduled report",
      error: error.message,
    });
  }
};

/**
 * Delete a scheduled report
 */
const deleteScheduledReport = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "deleteScheduledReport",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);
    const reportId = parseInt(req.params.id);

    if (!reportId) {
      return res.status(400).json({
        success: false,
        message: "Report ID is required",
      });
    }

    const deleted = await ReportScheduler.deleteScheduledReport(
      reportId,
      userId
    );

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: "Scheduled report not found or not authorized to delete",
      });
    }

    logger.info("Scheduled report deleted successfully", {
      requestId: req.requestId,
      userId,
      reportId,
    });

    res.status(200).json({
      success: true,
      message: "Scheduled report deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting scheduled report", {
      requestId: req.requestId,
      userId: req.user?.userId,
      reportId: req.params.id,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to delete scheduled report",
      error: error.message,
    });
  }
};

/**
 * Toggle the active status of a scheduled report
 */
const toggleReportStatus = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "toggleReportStatus",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);
    const reportId = parseInt(req.params.id);
    const { isActive } = req.body;

    if (!reportId) {
      return res.status(400).json({
        success: false,
        message: "Report ID is required",
      });
    }

    if (typeof isActive !== "boolean") {
      return res.status(400).json({
        success: false,
        message: "isActive must be a boolean value",
      });
    }

    const result = await ReportScheduler.toggleReportStatus(
      reportId,
      isActive,
      userId
    );

    if (!result) {
      return res.status(404).json({
        success: false,
        message: "Scheduled report not found or not authorized to update",
      });
    }

    logger.info("Report status toggled successfully", {
      requestId: req.requestId,
      userId,
      reportId,
      isActive,
    });

    res.status(200).json({
      success: true,
      message: `Report ${isActive ? "activated" : "deactivated"} successfully`,
      data: result,
    });
  } catch (error) {
    logger.error("Error toggling report status", {
      requestId: req.requestId,
      userId: req.user?.userId,
      reportId: req.params.id,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to toggle report status",
      error: error.message,
    });
  }
};

/**
 * Run a scheduled report immediately
 */
const runReportNow = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "runReportNow",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);
    const reportId = parseInt(req.params.id);

    if (!reportId) {
      return res.status(400).json({
        success: false,
        message: "Report ID is required",
      });
    }

    // Verify user owns the report
    const report = await ReportScheduler.getScheduledReportById(reportId);
    if (!report || report.user_id !== userId) {
      return res.status(404).json({
        success: false,
        message: "Scheduled report not found or not authorized to run",
      });
    }

    // Use the comprehensive report execution service
    const result = await reportExecutionService.executeReport(reportId, true);

    logger.info("Manual report execution completed", {
      requestId: req.requestId,
      userId,
      reportId,
      executionId: result.executionId,
      emailsSent: result.emailsSent,
    });

    res.status(200).json({
      success: true,
      message: "Report executed successfully. Emails have been sent.",
      data: result,
    });
  } catch (error) {
    logger.error("Error running report", {
      requestId: req.requestId,
      userId: req.user?.userId,
      reportId: req.params.id,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to run report",
      error: error.message,
    });
  }
};

/**
 * Get execution logs for a specific scheduled report
 */
const getExecutionLogs = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "getExecutionLogs",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);
    const reportId = parseInt(req.params.id);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    if (!reportId) {
      return res.status(400).json({
        success: false,
        message: "Report ID is required",
      });
    }

    const result = await ReportScheduler.getExecutionLogs(
      reportId,
      userId,
      page,
      limit
    );

    logger.info("Execution logs retrieved successfully", {
      requestId: req.requestId,
      userId,
      reportId,
      count: result.data.length,
    });

    res.status(200).json({
      success: true,
      message: "Execution logs retrieved successfully",
      data: {
        logs: result.data,
        totalRecords: result.totalRecords,
      },
    });
  } catch (error) {
    logger.error("Error getting execution logs", {
      requestId: req.requestId,
      userId: req.user?.userId,
      reportId: req.params.id,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve execution logs",
      error: error.message,
    });
  }
};

/**
 * Get all execution logs for all scheduled reports
 */
const getAllExecutionLogs = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "getAllExecutionLogs",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await ReportScheduler.getAllExecutionLogs(
      userId,
      page,
      limit
    );

    logger.info("All execution logs retrieved successfully", {
      requestId: req.requestId,
      userId,
      count: result.data.length,
    });

    res.status(200).json({
      success: true,
      message: "All execution logs retrieved successfully",
      data: {
        logs: result.data,
        totalRecords: result.totalRecords,
      },
    });
  } catch (error) {
    logger.error("Error getting all execution logs", {
      requestId: req.requestId,
      userId: req.user?.userId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve execution logs",
      error: error.message,
    });
  }
};

/**
 * Test email configuration
 */
const testEmailConfiguration = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "testEmailConfiguration",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const { recipients, ccRecipients } = req.body;

    if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
      return res.status(400).json({
        success: false,
        message: "At least one recipient email is required",
      });
    }

    const result = await emailService.sendTestEmail(
      recipients,
      ccRecipients || []
    );

    if (result.success) {
      logger.info("Test email sent successfully", {
        requestId: req.requestId,
        userId: req.user.userId,
        recipients: recipients.length,
        ccRecipients: (ccRecipients || []).length,
      });

      res.status(200).json({
        success: true,
        message: "Test email sent successfully",
        data: {
          emailsSent: result.emailsSent,
          messageIds: result.messageIds,
        },
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to send test email",
        error: result.error,
      });
    }
  } catch (error) {
    logger.error("Error testing email configuration", {
      requestId: req.requestId,
      userId: req.user?.userId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to test email configuration",
      error: error.message,
    });
  }
};

/**
 * Get available report types
 */
const getAvailableReportTypes = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "getAvailableReportTypes",
      req.requestId
    );

    const reportTypes = [
      {
        value: "Google Review Reports",
        label: "Google Review Reports",
        description:
          "Comprehensive reports on Google reviews with ratings analysis and response metrics",
      },
      {
        value: "Performance Analytics",
        label: "Performance Analytics",
        description:
          "Google My Business performance metrics including impressions, clicks, and customer actions",
      },
      {
        value: "Performance Trends",
        label: "Performance Trends",
        description:
          "Trend analysis of Google My Business performance over time",
      },
      {
        value: "Google Review Trends",
        label: "Google Review Trends",
        description:
          "Detailed analysis of review trends, response rates, and rating distributions",
      },
    ];

    res.status(200).json({
      success: true,
      message: "Available report types retrieved successfully",
      data: reportTypes,
    });
  } catch (error) {
    logger.error("Error getting available report types", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve available report types",
      error: error.message,
    });
  }
};

/**
 * Get scheduler statistics
 */
const getSchedulerStats = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "getSchedulerStats",
      req.requestId
    );

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = parseInt(req.user.userId);

    // Get basic statistics from database
    const stats = await ReportScheduler.getSchedulerStatistics(userId);

    // Get SES sending statistics
    const sesStats = await emailService.getSendingStatistics();

    res.status(200).json({
      success: true,
      message: "Scheduler statistics retrieved successfully",
      data: {
        ...stats,
        sesStatistics: sesStats.success ? sesStats.data : [],
      },
    });
  } catch (error) {
    logger.error("Error getting scheduler statistics", {
      requestId: req.requestId,
      userId: req.user?.userId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve scheduler statistics",
      error: error.message,
    });
  }
};

/**
 * Get reports that are due for execution (for sync service)
 */
const getDueReports = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "getDueReports",
      req.requestId
    );

    const dueReports = await ReportScheduler.getDueReports();

    logger.info("Due reports retrieved successfully", {
      requestId: req.requestId,
      count: dueReports.length,
    });

    res.status(200).json({
      success: true,
      message: "Due reports retrieved successfully",
      data: dueReports,
    });
  } catch (error) {
    logger.error("Error getting due reports", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve due reports",
      error: error.message,
    });
  }
};

/**
 * Execute a scheduled report (for sync service)
 */
const executeScheduledReport = async (req, res) => {
  try {
    logger.logControllerAction(
      "reportScheduler",
      "executeScheduledReport",
      req.requestId
    );

    const reportId = parseInt(req.params.id);

    if (!reportId) {
      return res.status(400).json({
        success: false,
        message: "Report ID is required",
      });
    }

    // Use the comprehensive report execution service
    const result = await reportExecutionService.executeReport(reportId, false);

    logger.info("Scheduled report executed successfully", {
      requestId: req.requestId,
      reportId,
      executionId: result.executionId,
      emailsSent: result.emailsSent,
    });

    res.status(200).json({
      success: true,
      message: "Report executed successfully",
      data: result,
    });
  } catch (error) {
    logger.error("Error executing scheduled report", {
      requestId: req.requestId,
      reportId: req.params.id,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Failed to execute scheduled report",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  getScheduledReports,
  createScheduledReport,
  updateScheduledReport,
  deleteScheduledReport,
  toggleReportStatus,
  runReportNow,
  getExecutionLogs,
  getAllExecutionLogs,
  testEmailConfiguration,
  getAvailableReportTypes,
  getSchedulerStats,
  getDueReports,
  executeScheduledReport,
};
