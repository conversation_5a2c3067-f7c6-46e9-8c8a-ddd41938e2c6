import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from "chart.js";
import { Pie } from "react-chartjs-2";

ChartJS.register(ArcElement, Tooltip, Legend);

interface IRatingDistributionChartProps {
  data: Record<number, number>;
  title?: string;
}

const RatingDistributionChart: React.FC<IRatingDistributionChartProps> = ({
  data,
  title = "Rating Distribution",
}) => {
  const theme = useTheme();
  const ratings = [1, 2, 3, 4, 5];
  const values = ratings.map((rating) => data[rating] || 0);
  const totalReviews = values.reduce((sum, val) => sum + val, 0);

  // Generate colors using reportColors from theme
  const getColorsForRatings = () => {
    const reportColors = theme.palette.starRatings;

    if (reportColors) {
      return {
        backgroundColor: [
          reportColors.one,
          reportColors.two,
          reportColors.three,
          reportColors.four,
          reportColors.five,
        ],
        borderColor: [
          reportColors.one,
          reportColors.two,
          reportColors.three,
          reportColors.four,
          reportColors.five,
        ],
      };
    }

    // Fallback to original colors if reportColors not available
    const primary = theme.palette.primary.main;
    const secondary = theme.palette.secondary.main;
    return {
      backgroundColor: [
        "#f44336", // 1 star - Red (negative)
        "#ff9800", // 2 stars - Orange (poor)
        "#ffc107", // 3 stars - Amber (average)
        primary, // 4 stars - Primary theme color (good)
        secondary, // 5 stars - Secondary theme color (excellent)
      ],
      borderColor: ["#d32f2f", "#f57c00", "#ffa000", primary, secondary],
    };
  };

  const colors = getColorsForRatings();

  const chartData = {
    labels: ratings.map((rating) => `${rating} Star${rating > 1 ? "s" : ""}`),
    datasets: [
      {
        data: values,
        backgroundColor: colors.backgroundColor,
        borderColor: colors.borderColor,
        borderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "right" as const,
        labels: {
          usePointStyle: true,
          pointStyle: "rect",
          boxWidth: 12,
          boxHeight: 12,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const value = context.parsed;
            const percentage =
              totalReviews > 0
                ? ((value / totalReviews) * 100).toFixed(1)
                : "0.0";
            return `${context.label}: ${value} (${percentage}%)`;
          },
        },
      },
    },
  };

  // Calculate average rating
  const averageRating =
    totalReviews > 0
      ? (
          ratings.reduce(
            (total, rating) => total + rating * (data[rating] || 0),
            0
          ) / totalReviews
        ).toFixed(1)
      : "0.0";

  return (
    <Box
      sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}
      data-chart-type="ratingdistribution"
      data-chart-title={title}
    >
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="h6"
          fontWeight="bold"
          gutterBottom
          className="chart-title"
        >
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 3, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Total Reviews: <strong>{totalReviews}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Average Rating: <strong>{averageRating} ⭐</strong>
          </Typography>
        </Box>
      </Box>

      {totalReviews > 0 ? (
        <Box sx={{ height: 400, display: "flex", justifyContent: "center" }}>
          <Pie data={chartData} options={options} />
        </Box>
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No data available for the selected filters
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default RatingDistributionChart;
